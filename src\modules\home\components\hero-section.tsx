'use client'

import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import React from 'react'

const HeroSection = () => {
  return (
    <section
      className="relative h-[500px] flex items-center bg-cover bg-center"
      style={{
        backgroundImage: `url("/images/hero.jpg")`,
      }}
    >
      {/* Overlay with gradient for stylish look */}
      <div className="absolute inset-0 bg-black/15"></div>

      {/* Content */}
      <div className="relative w-full container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-[70vh]">
          <h2 className="text-white text-6xl font-bold leading-tight mb-20 drop-shadow-md">
            INSTANT <br /> PART SEARCH
          </h2>

          {/* Search Bar */}
          <div className="flex items-center border-b-2 border-white/80 transition">
            <Input
              placeholder="SEARCH BY PART NUMBER / USE * AS A WILDCARD"
              className="flex-1 bg-transparent border-0 rounded-none text-white font-medium placeholder:text-white/80 uppercase tracking-wide focus:ring-0 focus:outline-none"
              style={{ boxShadow: 'none' }}
            />    
              <Search className="w-7 h-7 text-primary hover:text-white" />
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
